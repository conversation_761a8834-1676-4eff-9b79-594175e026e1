/// أدوات التحويل الآمن للبيانات
/// تمنع أخطاء type casting وتوفر قيم افتراضية آمنة
library;

/// تحويل آمن إلى int
int safeInt(dynamic value, {int defaultValue = 0}) {
  if (value == null) return defaultValue;
  if (value is int) return value;
  if (value is num) return value.toInt();
  if (value is String) {
    final parsed = int.tryParse(value);
    return parsed ?? defaultValue;
  }
  return defaultValue;
}

/// تحويل آمن إلى double
double safeDouble(dynamic value, {double defaultValue = 0.0}) {
  if (value == null) return defaultValue;
  if (value is double) return value;
  if (value is num) return value.toDouble();
  if (value is String) {
    final parsed = double.tryParse(value);
    return parsed ?? defaultValue;
  }
  return defaultValue;
}

/// تحويل آمن إلى String
String safeString(dynamic value, {String defaultValue = ''}) {
  if (value == null) return defaultValue;
  if (value is String) return value;
  return value.toString();
}

/// تحويل آمن إلى bool
bool safeBool(dynamic value, {bool defaultValue = false}) {
  if (value == null) return defaultValue;
  if (value is bool) return value;
  if (value is int) return value == 1;
  if (value is String) {
    final lower = value.toLowerCase();
    return lower == 'true' || lower == '1' || lower == 'yes';
  }
  return defaultValue;
}

/// تحويل آمن إلى DateTime
DateTime safeDateTime(dynamic value, {DateTime? defaultValue}) {
  final fallback = defaultValue ?? DateTime.now();

  if (value == null) return fallback;
  if (value is DateTime) return value;
  if (value is String) {
    final parsed = DateTime.tryParse(value);
    return parsed ?? fallback;
  }
  return fallback;
}

/// تحويل آمن من Map مع مفتاح محدد
T safeMapValue<T>(Map<String, dynamic> map, String key, T defaultValue) {
  final value = map[key];

  if (T == int) {
    return safeInt(value, defaultValue: defaultValue as int) as T;
  } else if (T == double) {
    return safeDouble(value, defaultValue: defaultValue as double) as T;
  } else if (T == String) {
    return safeString(value, defaultValue: defaultValue as String) as T;
  } else if (T == bool) {
    return safeBool(value, defaultValue: defaultValue as bool) as T;
  } else if (T == DateTime) {
    return safeDateTime(value, defaultValue: defaultValue as DateTime?) as T;
  }

  return value ?? defaultValue;
}

/// تحويل آمن لقائمة من Maps
List<Map<String, dynamic>> safeMapList(dynamic value) {
  if (value == null) return [];
  if (value is List<Map<String, dynamic>>) return value;
  if (value is List) {
    return value
        .whereType<Map<String, dynamic>>()
        .cast<Map<String, dynamic>>()
        .toList();
  }
  return [];
}

/// تحويل آمن للأرقام من قاعدة البيانات
class SafeDbCast {
  /// تحويل آمن لقيمة رقمية من قاعدة البيانات
  static double toDouble(dynamic value) {
    return safeDouble(value);
  }

  /// تحويل آمن لقيمة صحيحة من قاعدة البيانات
  static int toInt(dynamic value) {
    return safeInt(value);
  }

  /// تحويل آمن لنص من قاعدة البيانات
  static String toStringValue(dynamic value) {
    return safeString(value);
  }

  /// تحويل آمن لقيمة منطقية من قاعدة البيانات
  static bool toBool(dynamic value) {
    return safeBool(value);
  }

  /// تحويل آمن لتاريخ من قاعدة البيانات
  static DateTime toDateTime(dynamic value) {
    return safeDateTime(value);
  }

  /// تحويل آمن لقيمة اختيارية
  static T? toNullable<T>(dynamic value) {
    if (value == null) return null;

    if (T == int) {
      return safeInt(value) as T?;
    } else if (T == double) {
      return safeDouble(value) as T?;
    } else if (T == String) {
      return safeString(value) as T?;
    } else if (T == bool) {
      return safeBool(value) as T?;
    } else if (T == DateTime) {
      return safeDateTime(value) as T?;
    }

    return value as T?;
  }
}

/// امتداد للخرائط لتحويل آمن
extension SafeMapExtension on Map<String, dynamic> {
  /// الحصول على قيمة int آمنة
  int getInt(String key, {int defaultValue = 0}) {
    return safeInt(this[key], defaultValue: defaultValue);
  }

  /// الحصول على قيمة double آمنة
  double getDouble(String key, {double defaultValue = 0.0}) {
    return safeDouble(this[key], defaultValue: defaultValue);
  }

  /// الحصول على قيمة String آمنة
  String getString(String key, {String defaultValue = ''}) {
    return safeString(this[key], defaultValue: defaultValue);
  }

  /// الحصول على قيمة bool آمنة
  bool getBool(String key, {bool defaultValue = false}) {
    return safeBool(this[key], defaultValue: defaultValue);
  }

  /// الحصول على قيمة DateTime آمنة
  DateTime getDateTime(String key, {DateTime? defaultValue}) {
    return safeDateTime(this[key], defaultValue: defaultValue);
  }

  /// الحصول على قيمة اختيارية
  T? getNullable<T>(String key) {
    return SafeDbCast.toNullable<T>(this[key]);
  }
}

/// امتداد للقوائم لتحويل آمن
extension SafeListExtension on List<dynamic> {
  /// تحويل آمن لقائمة من الأرقام الصحيحة
  List<int> toIntList() {
    return map((e) => safeInt(e)).toList();
  }

  /// تحويل آمن لقائمة من الأرقام العشرية
  List<double> toDoubleList() {
    return map((e) => safeDouble(e)).toList();
  }

  /// تحويل آمن لقائمة من النصوص
  List<String> toStringList() {
    return map((e) => safeString(e)).toList();
  }
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // بدلاً من:
/// final amount = row['amount'] as double; // قد يسبب خطأ
/// 
/// // استخدم:
/// final amount = safeDouble(row['amount']); // آمن
/// 
/// // أو:
/// final amount = row.getDouble('amount'); // آمن مع امتداد
/// 
/// // أو:
/// final amount = SafeDbCast.toDouble(row['amount']); // آمن مع كلاس
///
/// // للنصوص:
/// final name = SafeDbCast.toStringValue(row['name']); // آمن للنصوص
/// ```
