/// نموذج المستخدم
/// يحتوي على جميع بيانات المستخدم والصلاحيات
class User {
  final int? id;
  final String username;
  final String passwordHash;
  final String fullName;
  final String email;
  final String? phone;
  final bool isActive;
  final bool isAdmin;
  final int? roleId;
  final String? roleName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final String? notes;

  const User({
    this.id,
    required this.username,
    required this.passwordHash,
    required this.fullName,
    required this.email,
    this.phone,
    required this.isActive,
    required this.isAdmin,
    this.roleId,
    this.roleName,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.notes,
  });

  /// إنشاء مستخدم من Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] as int?,
      username: (map['username'] as String?) ?? '',
      passwordHash: (map['password_hash'] as String?) ?? '',
      fullName: (map['full_name'] as String?) ?? '',
      email: (map['email'] as String?) ?? '',
      phone: map['phone'] as String?,
      isActive: ((map['is_active'] as int?) ?? 1) == 1,
      isAdmin: ((map['is_admin'] as int?) ?? 0) == 1,
      roleId: map['role_id'] as int?,
      roleName: map['role_name'] as String?,
      createdAt:
          DateTime.tryParse((map['created_at'] as String?) ?? '') ??
          DateTime.now(),
      updatedAt:
          DateTime.tryParse((map['updated_at'] as String?) ?? '') ??
          DateTime.now(),
      lastLoginAt: map['last_login_at'] != null
          ? DateTime.tryParse(map['last_login_at'] as String)
          : null,
      notes: map['notes'] as String?,
    );
  }

  /// تحويل المستخدم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'password_hash': passwordHash,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'is_active': isActive ? 1 : 0,
      'is_admin': isAdmin ? 1 : 0,
      'role_id': roleId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'notes': notes,
    };
  }

  /// إنشاء نسخة محدثة من المستخدم
  User copyWith({
    int? id,
    String? username,
    String? passwordHash,
    String? fullName,
    String? email,
    String? phone,
    bool? isActive,
    bool? isAdmin,
    int? roleId,
    String? roleName,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    String? notes,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      passwordHash: passwordHash ?? this.passwordHash,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      roleId: roleId ?? this.roleId,
      roleName: roleName ?? this.roleName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      notes: notes ?? this.notes,
    );
  }

  /// التحقق من صحة بيانات المستخدم
  bool isValid() {
    return username.isNotEmpty &&
        passwordHash.isNotEmpty &&
        fullName.isNotEmpty &&
        email.isNotEmpty &&
        email.contains('@');
  }

  /// الحصول على اسم العرض
  String get displayName => fullName.isNotEmpty ? fullName : username;

  /// التحقق من انتهاء صلاحية المستخدم
  bool get isExpired {
    // يمكن إضافة منطق انتهاء الصلاحية هنا
    return false;
  }

  /// الحصول على حالة المستخدم كنص
  String get statusText {
    if (!isActive) return 'غير نشط';
    if (isExpired) return 'منتهي الصلاحية';
    return 'نشط';
  }

  @override
  String toString() {
    return 'User{id: $id, username: $username, fullName: $fullName, isActive: $isActive, isAdmin: $isAdmin}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id && other.username == username;
  }

  @override
  int get hashCode => id.hashCode ^ username.hashCode;
}
