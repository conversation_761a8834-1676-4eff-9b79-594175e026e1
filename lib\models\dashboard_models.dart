/// نماذج البيانات للوحة التحكم المتقدمة
/// تحتوي على مؤشرات الأداء الرئيسية والبيانات المالية
library;

/// مؤشر أداء رئيسي (KPI)
class KPIModel {
  final String id;
  final String title;
  final String value;
  final String unit;
  final double percentage;
  final KPITrend trend;
  final String description;
  final DateTime lastUpdated;

  KPIModel({
    required this.id,
    required this.title,
    required this.value,
    required this.unit,
    required this.percentage,
    required this.trend,
    required this.description,
    required this.lastUpdated,
  });

  factory KPIModel.fromMap(Map<String, dynamic> map) {
    return KPIModel(
      id: map['id'] as String,
      title: map['title'] as String,
      value: map['value'] as String,
      unit: map['unit'] as String,
      percentage: (map['percentage'] as num).toDouble(),
      trend: KPITrend.values.firstWhere(
        (e) => e.name == map['trend'],
        orElse: () => KPITrend.stable,
      ),
      description: map['description'] as String,
      lastUpdated: DateTime.parse(map['last_updated'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'value': value,
      'unit': unit,
      'percentage': percentage,
      'trend': trend.name,
      'description': description,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// اتجاه مؤشر الأداء
enum KPITrend {
  up, // صاعد
  down, // هابط
  stable, // مستقر
}

/// ملخص مالي سريع
class FinancialSummary {
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;
  final double cashFlow;
  final int totalCustomers;
  final int totalSuppliers;
  final int totalInvoices;
  final DateTime periodStart;
  final DateTime periodEnd;

  FinancialSummary({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
    required this.cashFlow,
    required this.totalCustomers,
    required this.totalSuppliers,
    required this.totalInvoices,
    required this.periodStart,
    required this.periodEnd,
  });

  /// نسبة الربح
  double get profitMargin {
    if (totalRevenue == 0) return 0;
    return (netProfit / totalRevenue) * 100;
  }

  /// نسبة الخصوم إلى الأصول
  double get debtToAssetRatio {
    if (totalAssets == 0) return 0;
    return (totalLiabilities / totalAssets) * 100;
  }

  /// نسبة حقوق الملكية
  double get equityRatio {
    if (totalAssets == 0) return 0;
    return (totalEquity / totalAssets) * 100;
  }

  factory FinancialSummary.fromMap(Map<String, dynamic> map) {
    return FinancialSummary(
      totalRevenue: (map['total_revenue'] as num).toDouble(),
      totalExpenses: (map['total_expenses'] as num).toDouble(),
      netProfit: (map['net_profit'] as num).toDouble(),
      totalAssets: (map['total_assets'] as num).toDouble(),
      totalLiabilities: (map['total_liabilities'] as num).toDouble(),
      totalEquity: (map['total_equity'] as num).toDouble(),
      cashFlow: (map['cash_flow'] as num).toDouble(),
      totalCustomers: map['total_customers'] as int,
      totalSuppliers: map['total_suppliers'] as int,
      totalInvoices: map['total_invoices'] as int,
      periodStart: DateTime.parse(map['period_start'] as String),
      periodEnd: DateTime.parse(map['period_end'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'total_revenue': totalRevenue,
      'total_expenses': totalExpenses,
      'net_profit': netProfit,
      'total_assets': totalAssets,
      'total_liabilities': totalLiabilities,
      'total_equity': totalEquity,
      'cash_flow': cashFlow,
      'total_customers': totalCustomers,
      'total_suppliers': totalSuppliers,
      'total_invoices': totalInvoices,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
    };
  }
}

/// بيانات الرسم البياني
class ChartData {
  final String label;
  final double value;
  final DateTime date;
  final String category;

  ChartData({
    required this.label,
    required this.value,
    required this.date,
    required this.category,
  });

  factory ChartData.fromMap(Map<String, dynamic> map) {
    return ChartData(
      label: (map['label'] as String?) ?? '',
      value: (map['value'] as num?)?.toDouble() ?? 0.0,
      date: DateTime.tryParse((map['date'] as String?) ?? '') ?? DateTime.now(),
      category: (map['category'] as String?) ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'value': value,
      'date': date.toIso8601String(),
      'category': category,
    };
  }
}

/// نوع الرسم البياني
enum ChartType {
  line, // خطي
  bar, // أعمدة
  pie, // دائري
  area, // منطقة
  scatter, // نقطي
}

/// بيانات لوحة التحكم الكاملة
class DashboardData {
  final List<KPIModel> kpis;
  final FinancialSummary financialSummary;
  final List<ChartData> revenueChart;
  final List<ChartData> expenseChart;
  final List<ChartData> profitChart;
  final List<ChartData> cashFlowChart;
  final DateTime lastRefresh;

  DashboardData({
    required this.kpis,
    required this.financialSummary,
    required this.revenueChart,
    required this.expenseChart,
    required this.profitChart,
    required this.cashFlowChart,
    required this.lastRefresh,
  });

  factory DashboardData.empty() {
    return DashboardData(
      kpis: [],
      financialSummary: FinancialSummary(
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        totalAssets: 0,
        totalLiabilities: 0,
        totalEquity: 0,
        cashFlow: 0,
        totalCustomers: 0,
        totalSuppliers: 0,
        totalInvoices: 0,
        periodStart: DateTime.now().subtract(const Duration(days: 30)),
        periodEnd: DateTime.now(),
      ),
      revenueChart: [],
      expenseChart: [],
      profitChart: [],
      cashFlowChart: [],
      lastRefresh: DateTime.now(),
    );
  }
}

/// إعدادات لوحة التحكم
class DashboardSettings {
  final bool showKPIs;
  final bool showCharts;
  final bool showFinancialSummary;
  final bool autoRefresh;
  final int refreshInterval; // بالدقائق
  final List<String> visibleKPIs;
  final ChartType defaultChartType;

  DashboardSettings({
    this.showKPIs = true,
    this.showCharts = true,
    this.showFinancialSummary = true,
    this.autoRefresh = false,
    this.refreshInterval = 5,
    this.visibleKPIs = const [],
    this.defaultChartType = ChartType.line,
  });

  factory DashboardSettings.fromMap(Map<String, dynamic> map) {
    return DashboardSettings(
      showKPIs: map['show_kpis'] as bool? ?? true,
      showCharts: map['show_charts'] as bool? ?? true,
      showFinancialSummary: map['show_financial_summary'] as bool? ?? true,
      autoRefresh: map['auto_refresh'] as bool? ?? false,
      refreshInterval: map['refresh_interval'] as int? ?? 5,
      visibleKPIs: List<String>.from(map['visible_kpis'] ?? []),
      defaultChartType: ChartType.values.firstWhere(
        (e) => e.name == map['default_chart_type'],
        orElse: () => ChartType.line,
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'show_kpis': showKPIs,
      'show_charts': showCharts,
      'show_financial_summary': showFinancialSummary,
      'auto_refresh': autoRefresh,
      'refresh_interval': refreshInterval,
      'visible_kpis': visibleKPIs,
      'default_chart_type': defaultChartType.name,
    };
  }

  DashboardSettings copyWith({
    bool? showKPIs,
    bool? showCharts,
    bool? showFinancialSummary,
    bool? autoRefresh,
    int? refreshInterval,
    List<String>? visibleKPIs,
    ChartType? defaultChartType,
  }) {
    return DashboardSettings(
      showKPIs: showKPIs ?? this.showKPIs,
      showCharts: showCharts ?? this.showCharts,
      showFinancialSummary: showFinancialSummary ?? this.showFinancialSummary,
      autoRefresh: autoRefresh ?? this.autoRefresh,
      refreshInterval: refreshInterval ?? this.refreshInterval,
      visibleKPIs: visibleKPIs ?? this.visibleKPIs,
      defaultChartType: defaultChartType ?? this.defaultChartType,
    );
  }
}
