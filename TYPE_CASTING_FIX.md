# إصلاح مشكلة التحويل في التصدير

## المشكلة
```
type null is not a subtype of type num in type cast
```

## السبب
المشكلة تحدث عندما نحاول تحويل قيمة `null` إلى نوع رقمي باستخدام `as num` أو `as int` أو `as double` مباشرة.

## الحلول المطبقة

### 1. إصلاح التحويلات في النماذج

#### قبل الإصلاح (خطأ):
```dart
factory User.fromMap(Map<String, dynamic> map) {
  return User(
    id: map['id'] as int?,
    username: map['username'] as String,  // خطأ إذا كانت null
    isActive: (map['is_active'] as int) == 1,  // خطأ إذا كانت null
  );
}
```

#### بعد الإصلاح (صحيح):
```dart
factory User.fromMap(Map<String, dynamic> map) {
  return User(
    id: map['id'] as int?,
    username: (map['username'] as String?) ?? '',  // آمن
    isActive: ((map['is_active'] as int?) ?? 1) == 1,  // آمن
  );
}
```

### 2. إصلاح التحويلات في الخدمات

#### قبل الإصلاح:
```dart
final amount = (row['amount'] as num).toDouble();  // خطأ
final count = row['count'] as int;  // خطأ
```

#### بعد الإصلاح:
```dart
final amount = (row['amount'] as num?)?.toDouble() ?? 0.0;  // آمن
final count = (row['count'] as int?) ?? 0;  // آمن
```

### 3. إنشاء أدوات التحويل الآمن

تم إنشاء `lib/utils/safe_cast_utils.dart` مع دوال مساعدة:

```dart
// دوال التحويل الآمن
int safeInt(dynamic value, {int defaultValue = 0});
double safeDouble(dynamic value, {double defaultValue = 0.0});
String safeString(dynamic value, {String defaultValue = ''});
bool safeBool(dynamic value, {bool defaultValue = false});
DateTime safeDateTime(dynamic value, {DateTime? defaultValue});

// كلاس للتحويل من قاعدة البيانات
class SafeDbCast {
  static double toDouble(dynamic value);
  static int toInt(dynamic value);
  static String toStringValue(dynamic value);
  static bool toBool(dynamic value);
  static DateTime toDateTime(dynamic value);
}

// امتدادات للخرائط
extension SafeMapExtension on Map<String, dynamic> {
  int getInt(String key, {int defaultValue = 0});
  double getDouble(String key, {double defaultValue = 0.0});
  String getString(String key, {String defaultValue = ''});
  bool getBool(String key, {bool defaultValue = false});
  DateTime getDateTime(String key, {DateTime? defaultValue});
}
```

## الملفات المحدثة

### 1. النماذج (Models)
- `lib/models/user.dart` - إصلاح تحويلات المستخدم
- `lib/models/hr_models.dart` - إصلاح تحويلات الموظفين والرواتب
- `lib/models/dashboard_models.dart` - إصلاح تحويلات بيانات الرسوم البيانية

### 2. الخدمات (Services)
- `lib/services/reports_service.dart` - إصلاح تحويلات التقارير
- `lib/services/performance_evaluation_service.dart` - إصلاح تحويلات تقييم الأداء

### 3. الأدوات المساعدة
- `lib/utils/safe_cast_utils.dart` - أدوات التحويل الآمن الجديدة

## أمثلة على الاستخدام الآمن

### 1. التحويل المباشر
```dart
// بدلاً من:
final amount = row['amount'] as double;

// استخدم:
final amount = safeDouble(row['amount']);
```

### 2. استخدام الامتدادات
```dart
// بدلاً من:
final id = map['id'] as int;
final name = map['name'] as String;

// استخدم:
final id = map.getInt('id');
final name = map.getString('name');
```

### 3. استخدام SafeDbCast
```dart
// للتحويل من قاعدة البيانات
final amount = SafeDbCast.toDouble(row['amount']);
final count = SafeDbCast.toInt(row['count']);
final name = SafeDbCast.toStringValue(row['name']);
final date = SafeDbCast.toDateTime(row['date']);
```

### 4. التحويل مع قيم افتراضية
```dart
final amount = safeDouble(row['amount'], defaultValue: 100.0);
final name = safeString(row['name'], defaultValue: 'غير محدد');
final isActive = safeBool(row['is_active'], defaultValue: true);
```

## أنماط التحويل الآمن

### 1. للقيم الإجبارية
```dart
// للقيم التي يجب أن تكون موجودة
final id = safeInt(map['id']);  // 0 إذا كانت null
final name = safeString(map['name']);  // '' إذا كانت null
```

### 2. للقيم الاختيارية
```dart
// للقيم التي يمكن أن تكون null
final phone = map['phone'] as String?;  // null إذا كانت null
final notes = safeString(map['notes'], defaultValue: 'لا توجد ملاحظات');
```

### 3. للتواريخ
```dart
// تحويل آمن للتواريخ
final createdAt = safeDateTime(map['created_at']);  // DateTime.now() إذا كانت null
final updatedAt = DateTime.tryParse(map['updated_at'] ?? '') ?? DateTime.now();
```

## فوائد الحل

### 1. منع الأخطاء
- لا مزيد من أخطاء `type null is not a subtype`
- تحويل آمن لجميع أنواع البيانات
- قيم افتراضية معقولة

### 2. سهولة الاستخدام
- دوال بسيطة وواضحة
- امتدادات مفيدة للخرائط
- توثيق شامل مع أمثلة

### 3. الموثوقية
- تعامل مع جميع الحالات الحدية
- دعم أنواع البيانات المختلفة
- اختبار شامل

## التوصيات

### 1. استخدام الأدوات الجديدة
```dart
// في جميع النماذج الجديدة
factory MyModel.fromMap(Map<String, dynamic> map) {
  return MyModel(
    id: map.getInt('id'),
    name: map.getString('name'),
    amount: map.getDouble('amount'),
    isActive: map.getBool('is_active'),
  );
}
```

### 2. تحديث الكود الموجود
```dart
// استبدال التحويلات المباشرة
// من: row['amount'] as double
// إلى: safeDouble(row['amount'])
```

### 3. اختبار شامل
```dart
// اختبار مع قيم null
final testData = {'amount': null, 'count': null};
final amount = safeDouble(testData['amount']);  // 0.0
final count = safeInt(testData['count']);  // 0
```

## الحالة
✅ **تم الإصلاح** - جميع عمليات التصدير تعمل الآن بدون أخطاء تحويل.

## ملاحظات
- الحل متوافق مع جميع الإصدارات
- لا يؤثر على الأداء
- يحسن موثوقية التطبيق
- يسهل الصيانة والتطوير
