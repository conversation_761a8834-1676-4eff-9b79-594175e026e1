/// نماذج نظام الموارد البشرية
library;

import 'dart:convert';
import '../constants/app_constants.dart';

/// نموذج القسم
class Department {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int? managerId;
  final int? costCenterAccountId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Department({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.managerId,
    this.costCenterAccountId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Department.fromMap(Map<String, dynamic> map) {
    return Department(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      managerId: map['manager_id'] as int?,
      costCenterAccountId: map['cost_center_account_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'name': name,
      'description': description,
      'manager_id': managerId,
      'cost_center_account_id': costCenterAccountId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Department copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? managerId,
    int? costCenterAccountId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج المنصب
class Position {
  final int? id;
  final String code;
  final String title;
  final String? description;
  final int? departmentId;
  final double minSalary;
  final double maxSalary;
  final String? requirements;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Position({
    this.id,
    required this.code,
    required this.title,
    this.description,
    this.departmentId,
    this.minSalary = 0,
    this.maxSalary = 0,
    this.requirements,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Position.fromMap(Map<String, dynamic> map) {
    return Position(
      id: map['id'] as int?,
      code: map['code'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      departmentId: map['department_id'] as int?,
      minSalary: (map['min_salary'] as num).toDouble(),
      maxSalary: (map['max_salary'] as num).toDouble(),
      requirements: map['requirements'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'title': title,
      'description': description,
      'department_id': departmentId,
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'requirements': requirements,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Position copyWith({
    int? id,
    String? code,
    String? title,
    String? description,
    int? departmentId,
    double? minSalary,
    double? maxSalary,
    String? requirements,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Position(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      minSalary: minSalary ?? this.minSalary,
      maxSalary: maxSalary ?? this.maxSalary,
      requirements: requirements ?? this.requirements,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الموظف
class Employee {
  final int? id;
  final String employeeNumber;
  final String nationalId;
  final String firstName;
  final String lastName;
  final String fullName;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? maritalStatus;
  final String? phone;
  final String? email;
  final String? address;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final int? departmentId;
  final int? positionId;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final String status;
  final double basicSalary;
  final int? costCenterAccountId;
  final String? bankAccountNumber;
  final String? bankName;
  final String? socialInsuranceNumber;
  final String? taxNumber;
  final String? photoPath;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Employee({
    this.id,
    required this.employeeNumber,
    required this.nationalId,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.phone,
    this.email,
    this.address,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.departmentId,
    this.positionId,
    required this.hireDate,
    this.terminationDate,
    this.status = AppConstants.employeeStatusActive,
    this.basicSalary = 0,
    this.costCenterAccountId,
    this.bankAccountNumber,
    this.bankName,
    this.socialInsuranceNumber,
    this.taxNumber,
    this.photoPath,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'] as int?,
      employeeNumber: (map['employee_number'] as String?) ?? '',
      nationalId: (map['national_id'] as String?) ?? '',
      firstName: (map['first_name'] as String?) ?? '',
      lastName: (map['last_name'] as String?) ?? '',
      fullName: (map['full_name'] as String?) ?? '',
      dateOfBirth: map['date_of_birth'] != null
          ? DateTime.tryParse(map['date_of_birth'] as String)
          : null,
      gender: map['gender'] as String?,
      maritalStatus: map['marital_status'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      emergencyContactName: map['emergency_contact_name'] as String?,
      emergencyContactPhone: map['emergency_contact_phone'] as String?,
      departmentId: map['department_id'] as int?,
      positionId: map['position_id'] as int?,
      hireDate: DateTime.parse(map['hire_date'] as String),
      terminationDate: map['termination_date'] != null
          ? DateTime.parse(map['termination_date'] as String)
          : null,
      status: map['status'] as String,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      costCenterAccountId: map['cost_center_account_id'] as int?,
      bankAccountNumber: map['bank_account_number'] as String?,
      bankName: map['bank_name'] as String?,
      socialInsuranceNumber: map['social_insurance_number'] as String?,
      taxNumber: map['tax_number'] as String?,
      photoPath: map['photo_path'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_number': employeeNumber,
      'national_id': nationalId,
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'date_of_birth': dateOfBirth?.toIso8601String().split('T')[0],
      'gender': gender,
      'marital_status': maritalStatus,
      'phone': phone,
      'email': email,
      'address': address,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'department_id': departmentId,
      'position_id': positionId,
      'hire_date': hireDate.toIso8601String().split('T')[0],
      'termination_date': terminationDate?.toIso8601String().split('T')[0],
      'status': status,
      'basic_salary': basicSalary,
      'cost_center_account_id': costCenterAccountId,
      'bank_account_number': bankAccountNumber,
      'bank_name': bankName,
      'social_insurance_number': socialInsuranceNumber,
      'tax_number': taxNumber,
      'photo_path': photoPath,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Employee copyWith({
    int? id,
    String? employeeNumber,
    String? nationalId,
    String? firstName,
    String? lastName,
    String? fullName,
    DateTime? dateOfBirth,
    String? gender,
    String? maritalStatus,
    String? phone,
    String? email,
    String? address,
    String? emergencyContactName,
    String? emergencyContactPhone,
    int? departmentId,
    int? positionId,
    DateTime? hireDate,
    DateTime? terminationDate,
    String? status,
    double? basicSalary,
    int? costCenterAccountId,
    String? bankAccountNumber,
    String? bankName,
    String? socialInsuranceNumber,
    String? taxNumber,
    String? photoPath,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      employeeNumber: employeeNumber ?? this.employeeNumber,
      nationalId: nationalId ?? this.nationalId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      departmentId: departmentId ?? this.departmentId,
      positionId: positionId ?? this.positionId,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      status: status ?? this.status,
      basicSalary: basicSalary ?? this.basicSalary,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      bankName: bankName ?? this.bankName,
      socialInsuranceNumber:
          socialInsuranceNumber ?? this.socialInsuranceNumber,
      taxNumber: taxNumber ?? this.taxNumber,
      photoPath: photoPath ?? this.photoPath,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على الاسم الكامل
  String get displayName =>
      fullName.isNotEmpty ? fullName : '$firstName $lastName';
}

/// نموذج العقد
class EmployeeContract {
  final int? id;
  final int employeeId;
  final String contractType;
  final DateTime startDate;
  final DateTime? endDate;
  final double salary;
  final String workingHours;
  final int vacationDays;
  final String? benefits;
  final String? terms;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeContract({
    this.id,
    required this.employeeId,
    required this.contractType,
    required this.startDate,
    this.endDate,
    required this.salary,
    required this.workingHours,
    this.vacationDays = 21,
    this.benefits,
    this.terms,
    this.status = 'active',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeContract.fromMap(Map<String, dynamic> map) {
    return EmployeeContract(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      contractType: map['contract_type'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      salary: (map['salary'] as num).toDouble(),
      workingHours: map['working_hours'] as String,
      vacationDays: map['vacation_days'] as int,
      benefits: map['benefits'] as String?,
      terms: map['terms'] as String?,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'contract_type': contractType,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'salary': salary,
      'working_hours': workingHours,
      'vacation_days': vacationDays,
      'benefits': benefits,
      'terms': terms,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من العقد
  EmployeeContract copyWith({
    int? id,
    int? employeeId,
    String? contractType,
    DateTime? startDate,
    DateTime? endDate,
    double? salary,
    String? workingHours,
    int? vacationDays,
    String? benefits,
    String? terms,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeContract(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      contractType: contractType ?? this.contractType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      salary: salary ?? this.salary,
      workingHours: workingHours ?? this.workingHours,
      vacationDays: vacationDays ?? this.vacationDays,
      benefits: benefits ?? this.benefits,
      terms: terms ?? this.terms,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط العقد
  bool get isActive => status == 'active';

  /// التحقق من انتهاء العقد
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  /// التحقق من قرب انتهاء العقد (خلال 30 يوم)
  bool get isExpiringSoon =>
      endDate != null &&
      endDate!.isAfter(DateTime.now()) &&
      endDate!.isBefore(DateTime.now().add(const Duration(days: 30)));

  /// حساب مدة العقد بالأيام
  int get contractDurationDays {
    if (endDate == null) return -1; // عقد مفتوح
    return endDate!.difference(startDate).inDays;
  }

  /// حساب الأيام المتبقية في العقد
  int get remainingDays {
    if (endDate == null) return -1; // عقد مفتوح
    final remaining = endDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
}

/// نموذج الحضور
class Attendance {
  final int? id;
  final int employeeId;
  final DateTime attendanceDate;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final DateTime? breakStartTime;
  final DateTime? breakEndTime;
  final double totalHours;
  final double regularHours;
  final double overtimeHours;
  final int lateMinutes;
  final int earlyLeaveMinutes;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Attendance({
    this.id,
    required this.employeeId,
    required this.attendanceDate,
    this.checkInTime,
    this.checkOutTime,
    this.breakStartTime,
    this.breakEndTime,
    this.totalHours = 0,
    this.regularHours = 0,
    this.overtimeHours = 0,
    this.lateMinutes = 0,
    this.earlyLeaveMinutes = 0,
    this.status = 'present',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      attendanceDate: DateTime.parse(map['attendance_date'] as String),
      checkInTime: map['check_in_time'] != null
          ? DateTime.parse(map['check_in_time'] as String)
          : null,
      checkOutTime: map['check_out_time'] != null
          ? DateTime.parse(map['check_out_time'] as String)
          : null,
      breakStartTime: map['break_start_time'] != null
          ? DateTime.parse(map['break_start_time'] as String)
          : null,
      breakEndTime: map['break_end_time'] != null
          ? DateTime.parse(map['break_end_time'] as String)
          : null,
      totalHours: (map['total_hours'] as num).toDouble(),
      regularHours: (map['regular_hours'] as num).toDouble(),
      overtimeHours: (map['overtime_hours'] as num).toDouble(),
      lateMinutes: map['late_minutes'] as int,
      earlyLeaveMinutes: map['early_leave_minutes'] as int,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'attendance_date': attendanceDate.toIso8601String().split('T')[0],
      'check_in_time': checkInTime?.toIso8601String(),
      'check_out_time': checkOutTime?.toIso8601String(),
      'break_start_time': breakStartTime?.toIso8601String(),
      'break_end_time': breakEndTime?.toIso8601String(),
      'total_hours': totalHours,
      'regular_hours': regularHours,
      'overtime_hours': overtimeHours,
      'late_minutes': lateMinutes,
      'early_leave_minutes': earlyLeaveMinutes,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// التحقق من وجود تسجيل دخول
  bool get hasCheckIn => checkInTime != null;

  /// التحقق من وجود تسجيل خروج
  bool get hasCheckOut => checkOutTime != null;

  /// التحقق من اكتمال اليوم
  bool get isComplete => hasCheckIn && hasCheckOut;

  /// حساب ساعات العمل الفعلية
  double get actualWorkingHours {
    if (!isComplete) return 0;

    final workDuration = checkOutTime!.difference(checkInTime!);
    double hours = workDuration.inMinutes / 60.0;

    // خصم فترة الاستراحة إذا كانت موجودة
    if (breakStartTime != null && breakEndTime != null) {
      final breakDuration = breakEndTime!.difference(breakStartTime!);
      hours -= breakDuration.inMinutes / 60.0;
    }

    return hours > 0 ? hours : 0;
  }

  /// إنشاء نسخة معدلة من الحضور
  Attendance copyWith({
    int? id,
    int? employeeId,
    DateTime? attendanceDate,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    DateTime? breakStartTime,
    DateTime? breakEndTime,
    double? totalHours,
    double? regularHours,
    double? overtimeHours,
    int? lateMinutes,
    int? earlyLeaveMinutes,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      attendanceDate: attendanceDate ?? this.attendanceDate,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      breakStartTime: breakStartTime ?? this.breakStartTime,
      breakEndTime: breakEndTime ?? this.breakEndTime,
      totalHours: totalHours ?? this.totalHours,
      regularHours: regularHours ?? this.regularHours,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      lateMinutes: lateMinutes ?? this.lateMinutes,
      earlyLeaveMinutes: earlyLeaveMinutes ?? this.earlyLeaveMinutes,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الإجازة
class Leave {
  final int? id;
  final int employeeId;
  final String leaveType;
  final DateTime startDate;
  final DateTime endDate;
  final int totalDays;
  final String? reason;
  final String status;
  final int? requestedBy;
  final int? approvedBy;
  final DateTime? approvedAt;
  final String? rejectionReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Leave({
    this.id,
    required this.employeeId,
    required this.leaveType,
    required this.startDate,
    required this.endDate,
    required this.totalDays,
    this.reason,
    this.status = AppConstants.leaveStatusPending,
    this.requestedBy,
    this.approvedBy,
    this.approvedAt,
    this.rejectionReason,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Leave.fromMap(Map<String, dynamic> map) {
    return Leave(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      leaveType: map['leave_type'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      totalDays: map['total_days'] as int,
      reason: map['reason'] as String?,
      status: map['status'] as String,
      requestedBy: map['requested_by'] as int?,
      approvedBy: map['approved_by'] as int?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      rejectionReason: map['rejection_reason'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'leave_type': leaveType,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
      'total_days': totalDays,
      'reason': reason,
      'status': status,
      'requested_by': requestedBy,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Leave copyWith({
    int? id,
    int? employeeId,
    String? leaveType,
    DateTime? startDate,
    DateTime? endDate,
    int? totalDays,
    String? reason,
    String? status,
    int? requestedBy,
    int? approvedBy,
    DateTime? approvedAt,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Leave(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      leaveType: leaveType ?? this.leaveType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalDays: totalDays ?? this.totalDays,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      requestedBy: requestedBy ?? this.requestedBy,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة الإجازة
  bool get isPending => status == AppConstants.leaveStatusPending;
  bool get isApproved => status == AppConstants.leaveStatusApproved;
  bool get isRejected => status == AppConstants.leaveStatusRejected;
  bool get isCancelled => status == AppConstants.leaveStatusCancelled;

  /// حساب عدد أيام الإجازة
  int get calculatedDays {
    return endDate.difference(startDate).inDays + 1;
  }
}

/// نموذج وثائق الموظف
class EmployeeDocument {
  final int? id;
  final int employeeId;
  final String documentType;
  final String documentName;
  final String filePath;
  final String? description;
  final DateTime? expiryDate;
  final bool isRequired;
  final bool isActive;
  final DateTime uploadedAt;
  final int uploadedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeDocument({
    this.id,
    required this.employeeId,
    required this.documentType,
    required this.documentName,
    required this.filePath,
    this.description,
    this.expiryDate,
    this.isRequired = false,
    this.isActive = true,
    required this.uploadedAt,
    required this.uploadedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeDocument.fromMap(Map<String, dynamic> map) {
    return EmployeeDocument(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      documentType: map['document_type'] as String,
      documentName: map['document_name'] as String,
      filePath: map['file_path'] as String,
      description: map['description'] as String?,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      isRequired: (map['is_required'] as int) == 1,
      isActive: (map['is_active'] as int) == 1,
      uploadedAt: DateTime.parse(map['uploaded_at'] as String),
      uploadedBy: map['uploaded_by'] as int,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'document_type': documentType,
      'document_name': documentName,
      'file_path': filePath,
      'description': description,
      'expiry_date': expiryDate?.toIso8601String().split('T')[0],
      'is_required': isRequired ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'uploaded_at': uploadedAt.toIso8601String(),
      'uploaded_by': uploadedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeDocument copyWith({
    int? id,
    int? employeeId,
    String? documentType,
    String? documentName,
    String? filePath,
    String? description,
    DateTime? expiryDate,
    bool? isRequired,
    bool? isActive,
    DateTime? uploadedAt,
    int? uploadedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeDocument(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      documentType: documentType ?? this.documentType,
      documentName: documentName ?? this.documentName,
      filePath: filePath ?? this.filePath,
      description: description ?? this.description,
      expiryDate: expiryDate ?? this.expiryDate,
      isRequired: isRequired ?? this.isRequired,
      isActive: isActive ?? this.isActive,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من انتهاء صلاحية الوثيقة
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  /// الحصول على امتداد الملف
  String get fileExtension {
    return filePath.split('.').last.toLowerCase();
  }
}

/// نموذج الراتب
class Salary {
  final int? id;
  final int employeeId;
  final double basicSalary;
  final double totalAllowances;
  final double totalDeductions;
  final double grossSalary;
  final double netSalary;
  final DateTime effectiveFrom;
  final DateTime? effectiveTo;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Salary({
    this.id,
    required this.employeeId,
    required this.basicSalary,
    this.totalAllowances = 0,
    this.totalDeductions = 0,
    required this.grossSalary,
    required this.netSalary,
    required this.effectiveFrom,
    this.effectiveTo,
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Salary.fromMap(Map<String, dynamic> map) {
    return Salary(
      id: map['id'] as int?,
      employeeId: (map['employee_id'] as int?) ?? 0,
      basicSalary: (map['basic_salary'] as num?)?.toDouble() ?? 0.0,
      totalAllowances: (map['total_allowances'] as num?)?.toDouble() ?? 0,
      totalDeductions: (map['total_deductions'] as num?)?.toDouble() ?? 0,
      grossSalary: (map['gross_salary'] as num?)?.toDouble() ?? 0.0,
      netSalary: (map['net_salary'] as num?)?.toDouble() ?? 0.0,
      effectiveFrom:
          DateTime.tryParse((map['effective_from'] as String?) ?? '') ??
          DateTime.now(),
      effectiveTo: map['effective_to'] != null
          ? DateTime.tryParse(map['effective_to'] as String)
          : null,
      isActive: ((map['is_active'] as int?) ?? 1) == 1,
      notes: map['notes'] as String?,
      createdAt:
          DateTime.tryParse((map['created_at'] as String?) ?? '') ??
          DateTime.now(),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'basic_salary': basicSalary,
      'total_allowances': totalAllowances,
      'total_deductions': totalDeductions,
      'gross_salary': grossSalary,
      'net_salary': netSalary,
      'effective_from': effectiveFrom.toIso8601String().split('T')[0],
      'effective_to': effectiveTo?.toIso8601String().split('T')[0],
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Salary copyWith({
    int? id,
    int? employeeId,
    double? basicSalary,
    double? totalAllowances,
    double? totalDeductions,
    double? grossSalary,
    double? netSalary,
    DateTime? effectiveFrom,
    DateTime? effectiveTo,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Salary(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      basicSalary: basicSalary ?? this.basicSalary,
      totalAllowances: totalAllowances ?? this.totalAllowances,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      grossSalary: grossSalary ?? this.grossSalary,
      netSalary: netSalary ?? this.netSalary,
      effectiveFrom: effectiveFrom ?? this.effectiveFrom,
      effectiveTo: effectiveTo ?? this.effectiveTo,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من صحة الراتب
  bool get isValid => basicSalary > 0 && grossSalary >= basicSalary;

  /// حساب نسبة البدلات من الراتب الأساسي
  double get allowancesPercentage {
    return basicSalary > 0 ? (totalAllowances / basicSalary) * 100 : 0;
  }

  /// حساب نسبة الاستقطاعات من الراتب الإجمالي
  double get deductionsPercentage {
    return grossSalary > 0 ? (totalDeductions / grossSalary) * 100 : 0;
  }
}

/// نموذج تفاصيل الراتب
class SalaryDetail {
  final int? id;
  final int employeeId;
  final String componentType; // allowance, deduction, bonus
  final String componentName;
  final double amount;
  final bool isPercentage;
  final String? percentageOf; // basic_salary, gross_salary
  final bool isTaxable;
  final bool isActive;
  final DateTime effectiveFrom;
  final DateTime? effectiveTo;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalaryDetail({
    this.id,
    required this.employeeId,
    required this.componentType,
    required this.componentName,
    required this.amount,
    this.isPercentage = false,
    this.percentageOf,
    this.isTaxable = true,
    this.isActive = true,
    required this.effectiveFrom,
    this.effectiveTo,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalaryDetail.fromMap(Map<String, dynamic> map) {
    return SalaryDetail(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      componentType: map['component_type'] as String,
      componentName: map['component_name'] as String,
      amount: (map['amount'] as num).toDouble(),
      isPercentage: (map['is_percentage'] as int) == 1,
      percentageOf: map['percentage_of'] as String?,
      isTaxable: (map['is_taxable'] as int) == 1,
      isActive: (map['is_active'] as int) == 1,
      effectiveFrom: DateTime.parse(map['effective_from'] as String),
      effectiveTo: map['effective_to'] != null
          ? DateTime.parse(map['effective_to'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'component_type': componentType,
      'component_name': componentName,
      'amount': amount,
      'is_percentage': isPercentage ? 1 : 0,
      'percentage_of': percentageOf,
      'is_taxable': isTaxable ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'effective_from': effectiveFrom.toIso8601String().split('T')[0],
      'effective_to': effectiveTo?.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SalaryDetail copyWith({
    int? id,
    int? employeeId,
    String? componentType,
    String? componentName,
    double? amount,
    bool? isPercentage,
    String? percentageOf,
    bool? isTaxable,
    bool? isActive,
    DateTime? effectiveFrom,
    DateTime? effectiveTo,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalaryDetail(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      componentType: componentType ?? this.componentType,
      componentName: componentName ?? this.componentName,
      amount: amount ?? this.amount,
      isPercentage: isPercentage ?? this.isPercentage,
      percentageOf: percentageOf ?? this.percentageOf,
      isTaxable: isTaxable ?? this.isTaxable,
      isActive: isActive ?? this.isActive,
      effectiveFrom: effectiveFrom ?? this.effectiveFrom,
      effectiveTo: effectiveTo ?? this.effectiveTo,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نوع المكون
  bool get isAllowance =>
      componentType == AppConstants.salaryComponentAllowance;
  bool get isDeduction =>
      componentType == AppConstants.salaryComponentDeduction;
  bool get isBonus => componentType == AppConstants.salaryComponentBonus;

  /// حساب المبلغ الفعلي بناءً على النسبة المئوية
  double calculateActualAmount(double baseSalary, double grossSalary) {
    if (!isPercentage) return amount;

    switch (percentageOf) {
      case 'basic_salary':
        return (amount / 100) * baseSalary;
      case 'gross_salary':
        return (amount / 100) * grossSalary;
      default:
        return amount;
    }
  }
}

/// نموذج سجل كشف الراتب
class PayrollRecord {
  final int? id;
  final int employeeId;
  final int month;
  final int year;
  final double basicSalary;
  final double allowances;
  final double bonuses;
  final double overtimeAmount;
  final double grossSalary;
  final double incomeTax;
  final double socialInsurance;
  final double loanDeductions;
  final double otherDeductions;
  final double totalDeductions;
  final double netSalary;
  final int workingDays;
  final int actualWorkingDays;
  final double overtimeHours;
  final int absenceDays;
  final String status;
  final DateTime? paidDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PayrollRecord({
    this.id,
    required this.employeeId,
    required this.month,
    required this.year,
    required this.basicSalary,
    this.allowances = 0,
    this.bonuses = 0,
    this.overtimeAmount = 0,
    required this.grossSalary,
    this.incomeTax = 0,
    this.socialInsurance = 0,
    this.loanDeductions = 0,
    this.otherDeductions = 0,
    required this.totalDeductions,
    required this.netSalary,
    required this.workingDays,
    required this.actualWorkingDays,
    this.overtimeHours = 0,
    this.absenceDays = 0,
    this.status = 'calculated',
    this.paidDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PayrollRecord.fromMap(Map<String, dynamic> map) {
    return PayrollRecord(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      month: map['month'] as int,
      year: map['year'] as int,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      allowances: (map['allowances'] as num?)?.toDouble() ?? 0,
      bonuses: (map['bonuses'] as num?)?.toDouble() ?? 0,
      overtimeAmount: (map['overtime_amount'] as num?)?.toDouble() ?? 0,
      grossSalary: (map['gross_salary'] as num).toDouble(),
      incomeTax: (map['income_tax'] as num?)?.toDouble() ?? 0,
      socialInsurance: (map['social_insurance'] as num?)?.toDouble() ?? 0,
      loanDeductions: (map['loan_deductions'] as num?)?.toDouble() ?? 0,
      otherDeductions: (map['other_deductions'] as num?)?.toDouble() ?? 0,
      totalDeductions: (map['total_deductions'] as num).toDouble(),
      netSalary: (map['net_salary'] as num).toDouble(),
      workingDays: map['working_days'] as int,
      actualWorkingDays: map['actual_working_days'] as int,
      overtimeHours: (map['overtime_hours'] as num?)?.toDouble() ?? 0,
      absenceDays: map['absence_days'] as int? ?? 0,
      status: map['status'] as String? ?? 'calculated',
      paidDate: map['paid_date'] != null
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'month': month,
      'year': year,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'bonuses': bonuses,
      'overtime_amount': overtimeAmount,
      'gross_salary': grossSalary,
      'income_tax': incomeTax,
      'social_insurance': socialInsurance,
      'loan_deductions': loanDeductions,
      'other_deductions': otherDeductions,
      'total_deductions': totalDeductions,
      'net_salary': netSalary,
      'working_days': workingDays,
      'actual_working_days': actualWorkingDays,
      'overtime_hours': overtimeHours,
      'absence_days': absenceDays,
      'status': status,
      'paid_date': paidDate?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PayrollRecord copyWith({
    int? id,
    int? employeeId,
    int? month,
    int? year,
    double? basicSalary,
    double? allowances,
    double? bonuses,
    double? overtimeAmount,
    double? grossSalary,
    double? incomeTax,
    double? socialInsurance,
    double? loanDeductions,
    double? otherDeductions,
    double? totalDeductions,
    double? netSalary,
    int? workingDays,
    int? actualWorkingDays,
    double? overtimeHours,
    int? absenceDays,
    String? status,
    DateTime? paidDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PayrollRecord(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      month: month ?? this.month,
      year: year ?? this.year,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      bonuses: bonuses ?? this.bonuses,
      overtimeAmount: overtimeAmount ?? this.overtimeAmount,
      grossSalary: grossSalary ?? this.grossSalary,
      incomeTax: incomeTax ?? this.incomeTax,
      socialInsurance: socialInsurance ?? this.socialInsurance,
      loanDeductions: loanDeductions ?? this.loanDeductions,
      otherDeductions: otherDeductions ?? this.otherDeductions,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      netSalary: netSalary ?? this.netSalary,
      workingDays: workingDays ?? this.workingDays,
      actualWorkingDays: actualWorkingDays ?? this.actualWorkingDays,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      absenceDays: absenceDays ?? this.absenceDays,
      status: status ?? this.status,
      paidDate: paidDate ?? this.paidDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة كشف الراتب
  bool get isCalculated => status == 'calculated';
  bool get isPaid => status == 'paid';
  bool get isCancelled => status == 'cancelled';

  /// حساب نسبة الحضور
  double get attendancePercentage {
    return workingDays > 0 ? (actualWorkingDays / workingDays) * 100 : 0;
  }

  /// حساب نسبة الاستقطاعات
  double get deductionPercentage {
    return grossSalary > 0 ? (totalDeductions / grossSalary) * 100 : 0;
  }

  /// الحصول على اسم الشهر
  String get monthName {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return months[month - 1];
  }

  /// الحصول على فترة كشف الراتب
  String get payrollPeriod => '$monthName $year';

  /// التحقق من إمكانية التعديل
  bool get canEdit => status == 'calculated';

  /// التحقق من إمكانية الاعتماد
  bool get canApprove => status == 'calculated';

  /// التحقق من الاعتماد
  bool get isApproved => status == 'approved';
}

/// نموذج القرض
class Loan {
  final int? id;
  final int employeeId;
  final double amount;
  final double interestRate;
  final int installments;
  final double monthlyInstallment;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final DateTime loanDate;
  final DateTime? firstInstallmentDate;
  final String status;
  final String? purpose;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Loan({
    this.id,
    required this.employeeId,
    required this.amount,
    this.interestRate = 0,
    required this.installments,
    required this.monthlyInstallment,
    required this.totalAmount,
    this.paidAmount = 0,
    required this.remainingAmount,
    required this.loanDate,
    this.firstInstallmentDate,
    this.status = 'active',
    this.purpose,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Loan.fromMap(Map<String, dynamic> map) {
    return Loan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      interestRate: (map['interest_rate'] as num?)?.toDouble() ?? 0,
      installments: map['installments'] as int,
      monthlyInstallment: (map['monthly_installment'] as num).toDouble(),
      totalAmount: (map['total_amount'] as num).toDouble(),
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0,
      remainingAmount: (map['remaining_amount'] as num).toDouble(),
      loanDate: DateTime.parse(map['loan_date'] as String),
      firstInstallmentDate: map['first_installment_date'] != null
          ? DateTime.parse(map['first_installment_date'] as String)
          : null,
      status: map['status'] as String? ?? 'active',
      purpose: map['purpose'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'amount': amount,
      'interest_rate': interestRate,
      'installments': installments,
      'monthly_installment': monthlyInstallment,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'loan_date': loanDate.toIso8601String().split('T')[0],
      'first_installment_date': firstInstallmentDate?.toIso8601String().split(
        'T',
      )[0],
      'status': status,
      'purpose': purpose,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Loan copyWith({
    int? id,
    int? employeeId,
    double? amount,
    double? interestRate,
    int? installments,
    double? monthlyInstallment,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    DateTime? loanDate,
    DateTime? firstInstallmentDate,
    String? status,
    String? purpose,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Loan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      amount: amount ?? this.amount,
      interestRate: interestRate ?? this.interestRate,
      installments: installments ?? this.installments,
      monthlyInstallment: monthlyInstallment ?? this.monthlyInstallment,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      loanDate: loanDate ?? this.loanDate,
      firstInstallmentDate: firstInstallmentDate ?? this.firstInstallmentDate,
      status: status ?? this.status,
      purpose: purpose ?? this.purpose,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة القرض
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  /// حساب نسبة السداد
  double get paymentPercentage {
    return totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
  }

  /// عدد الأقساط المتبقية
  int get remainingInstallments => monthlyInstallment > 0
      ? (remainingAmount / monthlyInstallment).ceil()
      : 0;
}

/// نموذج قسط القرض
class LoanInstallment {
  final int? id;
  final int loanId;
  final int installmentNumber;
  final double amount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final double paidAmount;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LoanInstallment({
    this.id,
    required this.loanId,
    required this.installmentNumber,
    required this.amount,
    required this.dueDate,
    this.paidDate,
    this.paidAmount = 0,
    this.status = 'pending',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LoanInstallment.fromMap(Map<String, dynamic> map) {
    return LoanInstallment(
      id: map['id'] as int?,
      loanId: map['loan_id'] as int,
      installmentNumber: map['installment_number'] as int,
      amount: (map['amount'] as num).toDouble(),
      dueDate: DateTime.parse(map['due_date'] as String),
      paidDate: map['paid_date'] != null
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0,
      status: map['status'] as String? ?? 'pending',
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'loan_id': loanId,
      'installment_number': installmentNumber,
      'amount': amount,
      'due_date': dueDate.toIso8601String().split('T')[0],
      'paid_date': paidDate?.toIso8601String().split('T')[0],
      'paid_amount': paidAmount,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  LoanInstallment copyWith({
    int? id,
    int? loanId,
    int? installmentNumber,
    double? amount,
    DateTime? dueDate,
    DateTime? paidDate,
    double? paidAmount,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LoanInstallment(
      id: id ?? this.id,
      loanId: loanId ?? this.loanId,
      installmentNumber: installmentNumber ?? this.installmentNumber,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة القسط
  bool get isPaid => status == 'paid';
  bool get isPending => status == 'pending';
  bool get isOverdue => status == 'overdue';

  /// التحقق من تأخر القسط
  bool get isLate => isPending && dueDate.isBefore(DateTime.now());

  /// التحقق من قرب استحقاق القسط
  bool get isDueSoon =>
      status == 'pending' &&
      dueDate.isAfter(DateTime.now()) &&
      dueDate.isBefore(DateTime.now().add(const Duration(days: 7)));
}

/// نموذج قالب الراتب
class SalaryTemplate {
  final int? id;
  final String name;
  final String? description;
  final double basicSalary;
  final List<SalaryComponent> components;
  final bool isActive;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalaryTemplate({
    this.id,
    required this.name,
    this.description,
    this.basicSalary = 0,
    this.components = const [],
    this.isActive = true,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalaryTemplate.fromMap(Map<String, dynamic> map) {
    List<SalaryComponent> componentsList = [];
    if (map['components'] != null) {
      final componentsJson = map['components'] as String;
      final List<dynamic> componentsData = jsonDecode(componentsJson);
      componentsList = componentsData
          .map((data) => SalaryComponent.fromMap(data))
          .toList();
    }

    return SalaryTemplate(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      basicSalary: (map['basic_salary'] as num?)?.toDouble() ?? 0,
      components: componentsList,
      isActive: (map['is_active'] as int) == 1,
      isDefault: (map['is_default'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'basic_salary': basicSalary,
      'components': jsonEncode(components.map((c) => c.toMap()).toList()),
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SalaryTemplate copyWith({
    int? id,
    String? name,
    String? description,
    double? basicSalary,
    List<SalaryComponent>? components,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalaryTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      basicSalary: basicSalary ?? this.basicSalary,
      components: components ?? this.components,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب إجمالي البدلات
  double get totalAllowances {
    return components
        .where((c) => c.type == 'allowance')
        .fold(0, (sum, c) => sum + c.calculateAmount(basicSalary));
  }

  /// حساب إجمالي الخصومات
  double get totalDeductions {
    return components
        .where((c) => c.type == 'deduction')
        .fold(0, (sum, c) => sum + c.calculateAmount(basicSalary));
  }

  /// حساب الراتب الإجمالي
  double get grossSalary => basicSalary + totalAllowances;

  /// حساب الراتب الصافي
  double get netSalary => grossSalary - totalDeductions;
}

/// نموذج مكون الراتب
class SalaryComponent {
  final int? id;
  final String name;
  final String code;
  final String type; // allowance, deduction, bonus
  final String? description;
  final double defaultAmount;
  final bool isPercentage;
  final String? percentageOf; // basic_salary, gross_salary
  final bool isTaxable;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalaryComponent({
    this.id,
    required this.name,
    required this.code,
    required this.type,
    this.description,
    required this.defaultAmount,
    this.isPercentage = false,
    this.percentageOf,
    this.isTaxable = true,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalaryComponent.fromMap(Map<String, dynamic> map) {
    return SalaryComponent(
      id: map['id'] as int?,
      name: map['name'] as String,
      code: map['code'] as String,
      type: map['type'] as String,
      description: map['description'] as String?,
      defaultAmount: (map['default_amount'] as num).toDouble(),
      isPercentage: map['is_percentage'] as bool? ?? false,
      percentageOf: map['percentage_of'] as String?,
      isTaxable: map['is_taxable'] as bool? ?? true,
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'code': code,
      'type': type,
      'description': description,
      'default_amount': defaultAmount,
      'is_percentage': isPercentage,
      'percentage_of': percentageOf,
      'is_taxable': isTaxable,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  SalaryComponent copyWith({
    int? id,
    String? name,
    String? code,
    String? type,
    String? description,
    double? defaultAmount,
    bool? isPercentage,
    String? percentageOf,
    bool? isTaxable,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalaryComponent(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      type: type ?? this.type,
      description: description ?? this.description,
      defaultAmount: defaultAmount ?? this.defaultAmount,
      isPercentage: isPercentage ?? this.isPercentage,
      percentageOf: percentageOf ?? this.percentageOf,
      isTaxable: isTaxable ?? this.isTaxable,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب المبلغ الفعلي للمكون
  double calculateAmount(double basicSalary, [double? grossSalary]) {
    if (!isPercentage) return defaultAmount;

    switch (percentageOf) {
      case 'basic_salary':
        return basicSalary * (defaultAmount / 100);
      case 'gross_salary':
        return (grossSalary ?? basicSalary) * (defaultAmount / 100);
      default:
        return basicSalary * (defaultAmount / 100);
    }
  }

  /// الحصول على نوع المكون باللغة العربية
  String get typeInArabic {
    switch (type) {
      case 'allowance':
        return 'بدل';
      case 'deduction':
        return 'استقطاع';
      case 'bonus':
        return 'حافز';
      default:
        return type;
    }
  }

  /// التحقق من نوع المكون
  bool get isAllowance => type == 'allowance';
  bool get isDeduction => type == 'deduction';
  bool get isBonus => type == 'bonus';
}

/// نموذج الموافقة
class Approval {
  final int? id;
  final String requestType; // leave, loan, overtime, expense, training, other
  final int requestId;
  final int employeeId;
  final int? approverId;
  final String status; // pending, approved, rejected, cancelled
  final String priority; // low, normal, high, urgent
  final String title;
  final String? description;
  final String? requestData; // JSON data for the specific request
  final String? approvalNotes;
  final String? rejectionReason;
  final DateTime requestedAt;
  final DateTime? approvedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Approval({
    this.id,
    required this.requestType,
    required this.requestId,
    required this.employeeId,
    this.approverId,
    this.status = 'pending',
    this.priority = 'normal',
    required this.title,
    this.description,
    this.requestData,
    this.approvalNotes,
    this.rejectionReason,
    required this.requestedAt,
    this.approvedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Approval.fromMap(Map<String, dynamic> map) {
    return Approval(
      id: map['id'] as int?,
      requestType: map['request_type'] as String,
      requestId: map['request_id'] as int,
      employeeId: map['employee_id'] as int,
      approverId: map['approver_id'] as int?,
      status: map['status'] as String? ?? 'pending',
      priority: map['priority'] as String? ?? 'normal',
      title: map['title'] as String,
      description: map['description'] as String?,
      requestData: map['request_data'] as String?,
      approvalNotes: map['approval_notes'] as String?,
      rejectionReason: map['rejection_reason'] as String?,
      requestedAt: DateTime.parse(map['requested_at'] as String),
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'request_type': requestType,
      'request_id': requestId,
      'employee_id': employeeId,
      'approver_id': approverId,
      'status': status,
      'priority': priority,
      'title': title,
      'description': description,
      'request_data': requestData,
      'approval_notes': approvalNotes,
      'rejection_reason': rejectionReason,
      'requested_at': requestedAt.toIso8601String(),
      'approved_at': approvedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Approval copyWith({
    int? id,
    String? requestType,
    int? requestId,
    int? employeeId,
    int? approverId,
    String? status,
    String? priority,
    String? title,
    String? description,
    String? requestData,
    String? approvalNotes,
    String? rejectionReason,
    DateTime? requestedAt,
    DateTime? approvedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Approval(
      id: id ?? this.id,
      requestType: requestType ?? this.requestType,
      requestId: requestId ?? this.requestId,
      employeeId: employeeId ?? this.employeeId,
      approverId: approverId ?? this.approverId,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      description: description ?? this.description,
      requestData: requestData ?? this.requestData,
      approvalNotes: approvalNotes ?? this.approvalNotes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      requestedAt: requestedAt ?? this.requestedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة الموافقة
  bool get isPending => status == 'pending';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';
  bool get isCancelled => status == 'cancelled';

  /// التحقق من أولوية الطلب
  bool get isUrgent => priority == 'urgent';
  bool get isHigh => priority == 'high';
  bool get isNormal => priority == 'normal';
  bool get isLow => priority == 'low';

  /// الحصول على نوع الطلب باللغة العربية
  String get requestTypeInArabic {
    switch (requestType) {
      case 'leave':
        return 'إجازة';
      case 'loan':
        return 'قرض';
      case 'overtime':
        return 'ساعات إضافية';
      case 'expense':
        return 'مصروف';
      case 'training':
        return 'تدريب';
      case 'other':
        return 'أخرى';
      default:
        return requestType;
    }
  }

  /// الحصول على حالة الموافقة باللغة العربية
  String get statusInArabic {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'approved':
        return 'موافق عليه';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  /// الحصول على الأولوية باللغة العربية
  String get priorityInArabic {
    switch (priority) {
      case 'urgent':
        return 'عاجل';
      case 'high':
        return 'عالي';
      case 'normal':
        return 'عادي';
      case 'low':
        return 'منخفض';
      default:
        return priority;
    }
  }
}

/// نموذج الوثيقة
class Document {
  final int? id;
  final int employeeId;
  final String
  documentType; // contract, id_copy, certificate, photo, cv, medical, other
  final String documentName;
  final String filePath;
  final int fileSize;
  final String? mimeType;
  final String? description;
  final bool isConfidential;
  final DateTime? expiryDate;
  final int? uploadedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Document({
    this.id,
    required this.employeeId,
    required this.documentType,
    required this.documentName,
    required this.filePath,
    this.fileSize = 0,
    this.mimeType,
    this.description,
    this.isConfidential = false,
    this.expiryDate,
    this.uploadedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Document.fromMap(Map<String, dynamic> map) {
    return Document(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      documentType: map['document_type'] as String,
      documentName: map['document_name'] as String,
      filePath: map['file_path'] as String,
      fileSize: map['file_size'] as int? ?? 0,
      mimeType: map['mime_type'] as String?,
      description: map['description'] as String?,
      isConfidential: (map['is_confidential'] as int?) == 1,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      uploadedBy: map['uploaded_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'document_type': documentType,
      'document_name': documentName,
      'file_path': filePath,
      'file_size': fileSize,
      'mime_type': mimeType,
      'description': description,
      'is_confidential': isConfidential ? 1 : 0,
      'expiry_date': expiryDate?.toIso8601String().split('T')[0],
      'uploaded_by': uploadedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Document copyWith({
    int? id,
    int? employeeId,
    String? documentType,
    String? documentName,
    String? filePath,
    int? fileSize,
    String? mimeType,
    String? description,
    bool? isConfidential,
    DateTime? expiryDate,
    int? uploadedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Document(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      documentType: documentType ?? this.documentType,
      documentName: documentName ?? this.documentName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      description: description ?? this.description,
      isConfidential: isConfidential ?? this.isConfidential,
      expiryDate: expiryDate ?? this.expiryDate,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من انتهاء صلاحية الوثيقة
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  /// الحصول على امتداد الملف
  String get fileExtension {
    return filePath.split('.').last.toLowerCase();
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// الحصول على نوع الوثيقة باللغة العربية
  String get documentTypeInArabic {
    switch (documentType) {
      case 'contract':
        return 'عقد';
      case 'id_copy':
        return 'صورة هوية';
      case 'certificate':
        return 'شهادة';
      case 'photo':
        return 'صورة شخصية';
      case 'cv':
        return 'سيرة ذاتية';
      case 'medical':
        return 'تقرير طبي';
      case 'other':
        return 'أخرى';
      default:
        return documentType;
    }
  }

  /// التحقق من نوع الملف
  bool get isImage =>
      ['jpg', 'jpeg', 'png', 'gif', 'bmp'].contains(fileExtension);
  bool get isPdf => fileExtension == 'pdf';
  bool get isDocument => ['doc', 'docx', 'txt', 'rtf'].contains(fileExtension);
  bool get isSpreadsheet => ['xls', 'xlsx', 'csv'].contains(fileExtension);
}

/// نموذج التنبيه
class Notification {
  final int? id;
  final int? employeeId; // null for system-wide notifications
  final String type; // reminder, alert, info, warning, error
  final String title;
  final String message;
  final String? actionType; // view, approve, pay, etc.
  final int? actionId; // related entity ID
  final String? actionData; // JSON data for the action
  final bool isRead;
  final bool isImportant;
  final DateTime? scheduledAt;
  final DateTime? readAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Notification({
    this.id,
    this.employeeId,
    required this.type,
    required this.title,
    required this.message,
    this.actionType,
    this.actionId,
    this.actionData,
    this.isRead = false,
    this.isImportant = false,
    this.scheduledAt,
    this.readAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Notification.fromMap(Map<String, dynamic> map) {
    return Notification(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int?,
      type: map['type'] as String,
      title: map['title'] as String,
      message: map['message'] as String,
      actionType: map['action_type'] as String?,
      actionId: map['action_id'] as int?,
      actionData: map['action_data'] as String?,
      isRead: (map['is_read'] as int?) == 1,
      isImportant: (map['is_important'] as int?) == 1,
      scheduledAt: map['scheduled_at'] != null
          ? DateTime.parse(map['scheduled_at'] as String)
          : null,
      readAt: map['read_at'] != null
          ? DateTime.parse(map['read_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'type': type,
      'title': title,
      'message': message,
      'action_type': actionType,
      'action_id': actionId,
      'action_data': actionData,
      'is_read': isRead ? 1 : 0,
      'is_important': isImportant ? 1 : 0,
      'scheduled_at': scheduledAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Notification copyWith({
    int? id,
    int? employeeId,
    String? type,
    String? title,
    String? message,
    String? actionType,
    int? actionId,
    String? actionData,
    bool? isRead,
    bool? isImportant,
    DateTime? scheduledAt,
    DateTime? readAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Notification(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      actionType: actionType ?? this.actionType,
      actionId: actionId ?? this.actionId,
      actionData: actionData ?? this.actionData,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant ?? this.isImportant,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة التنبيه
  bool get isUnread => !isRead;
  bool get hasAction => actionType != null && actionId != null;

  /// التحقق من نوع التنبيه
  bool get isReminder => type == 'reminder';
  bool get isAlert => type == 'alert';
  bool get isInfo => type == 'info';
  bool get isWarning => type == 'warning';
  bool get isError => type == 'error';

  /// الحصول على نوع التنبيه باللغة العربية
  String get typeInArabic {
    switch (type) {
      case 'reminder':
        return 'تذكير';
      case 'alert':
        return 'تنبيه';
      case 'info':
        return 'معلومات';
      case 'warning':
        return 'تحذير';
      case 'error':
        return 'خطأ';
      default:
        return type;
    }
  }

  /// التحقق من التنبيه المجدول
  bool get isScheduled => scheduledAt != null;
  bool get isDue => scheduledAt != null && DateTime.now().isAfter(scheduledAt!);

  /// التحقق من التنبيه للنظام بالكامل
  bool get isSystemWide => employeeId == null;
}

/// نموذج برنامج التدريب
class TrainingProgram {
  final int? id;
  final String name;
  final String? description;
  final int durationHours;
  final double cost;
  final String? category;
  final String? prerequisites;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingProgram({
    this.id,
    required this.name,
    this.description,
    this.durationHours = 0,
    this.cost = 0,
    this.category,
    this.prerequisites,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingProgram.fromMap(Map<String, dynamic> map) {
    return TrainingProgram(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      durationHours: map['duration_hours'] as int? ?? 0,
      cost: (map['cost'] as num?)?.toDouble() ?? 0,
      category: map['category'] as String?,
      prerequisites: map['prerequisites'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'duration_hours': durationHours,
      'cost': cost,
      'category': category,
      'prerequisites': prerequisites,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingProgram copyWith({
    int? id,
    String? name,
    String? description,
    int? durationHours,
    double? cost,
    String? category,
    String? prerequisites,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingProgram(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      durationHours: durationHours ?? this.durationHours,
      cost: cost ?? this.cost,
      category: category ?? this.category,
      prerequisites: prerequisites ?? this.prerequisites,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج جلسة التدريب
class TrainingSession {
  final int? id;
  final int programId;
  final String sessionName;
  final DateTime startDate;
  final DateTime endDate;
  final String? trainerName;
  final String? trainerEmail;
  final String? location;
  final int maxParticipants;
  final int currentParticipants;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingSession({
    this.id,
    required this.programId,
    required this.sessionName,
    required this.startDate,
    required this.endDate,
    this.trainerName,
    this.trainerEmail,
    this.location,
    this.maxParticipants = 0,
    this.currentParticipants = 0,
    this.status = 'scheduled',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingSession.fromMap(Map<String, dynamic> map) {
    return TrainingSession(
      id: map['id'] as int?,
      programId: map['program_id'] as int,
      sessionName: map['session_name'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      trainerName: map['trainer_name'] as String?,
      trainerEmail: map['trainer_email'] as String?,
      location: map['location'] as String?,
      maxParticipants: map['max_participants'] as int? ?? 0,
      currentParticipants: map['current_participants'] as int? ?? 0,
      status: map['status'] as String? ?? 'scheduled',
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'program_id': programId,
      'session_name': sessionName,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'trainer_name': trainerName,
      'trainer_email': trainerEmail,
      'location': location,
      'max_participants': maxParticipants,
      'current_participants': currentParticipants,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingSession copyWith({
    int? id,
    int? programId,
    String? sessionName,
    DateTime? startDate,
    DateTime? endDate,
    String? trainerName,
    String? trainerEmail,
    String? location,
    int? maxParticipants,
    int? currentParticipants,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingSession(
      id: id ?? this.id,
      programId: programId ?? this.programId,
      sessionName: sessionName ?? this.sessionName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      trainerName: trainerName ?? this.trainerName,
      trainerEmail: trainerEmail ?? this.trainerEmail,
      location: location ?? this.location,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من توفر مقاعد
  bool get hasAvailableSeats => currentParticipants < maxParticipants;

  /// عدد المقاعد المتاحة
  int get availableSeats => maxParticipants - currentParticipants;

  /// التحقق من انتهاء الجلسة
  bool get isCompleted => status == 'completed';

  /// التحقق من إلغاء الجلسة
  bool get isCancelled => status == 'cancelled';

  /// التحقق من بدء الجلسة
  bool get isStarted => DateTime.now().isAfter(startDate);
}

/// نموذج تسجيل التدريب
class TrainingEnrollment {
  final int? id;
  final int sessionId;
  final int employeeId;
  final DateTime enrollmentDate;
  final DateTime? completionDate;
  final String status;
  final double? score;
  final bool certificateIssued;
  final String? feedback;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingEnrollment({
    this.id,
    required this.sessionId,
    required this.employeeId,
    required this.enrollmentDate,
    this.completionDate,
    this.status = 'enrolled',
    this.score,
    this.certificateIssued = false,
    this.feedback,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingEnrollment.fromMap(Map<String, dynamic> map) {
    return TrainingEnrollment(
      id: map['id'] as int?,
      sessionId: map['session_id'] as int,
      employeeId: map['employee_id'] as int,
      enrollmentDate: DateTime.parse(map['enrollment_date'] as String),
      completionDate: map['completion_date'] != null
          ? DateTime.parse(map['completion_date'] as String)
          : null,
      status: map['status'] as String? ?? 'enrolled',
      score: (map['score'] as num?)?.toDouble(),
      certificateIssued: (map['certificate_issued'] as int) == 1,
      feedback: map['feedback'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'session_id': sessionId,
      'employee_id': employeeId,
      'enrollment_date': enrollmentDate.toIso8601String(),
      'completion_date': completionDate?.toIso8601String(),
      'status': status,
      'score': score,
      'certificate_issued': certificateIssued ? 1 : 0,
      'feedback': feedback,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingEnrollment copyWith({
    int? id,
    int? sessionId,
    int? employeeId,
    DateTime? enrollmentDate,
    DateTime? completionDate,
    String? status,
    double? score,
    bool? certificateIssued,
    String? feedback,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingEnrollment(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      employeeId: employeeId ?? this.employeeId,
      enrollmentDate: enrollmentDate ?? this.enrollmentDate,
      completionDate: completionDate ?? this.completionDate,
      status: status ?? this.status,
      score: score ?? this.score,
      certificateIssued: certificateIssued ?? this.certificateIssued,
      feedback: feedback ?? this.feedback,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من اكتمال التدريب
  bool get isCompleted => status == 'completed';

  /// التحقق من إلغاء التسجيل
  bool get isCancelled => status == 'cancelled';

  /// التحقق من التقدم
  bool get isInProgress => status == 'in_progress';

  /// التحقق من إصدار الشهادة
  bool get hasCertificate => certificateIssued;
}

/// نموذج دورة تقييم الأداء
class PerformanceCycle {
  final int? id;
  final String cycleName;
  final DateTime startDate;
  final DateTime endDate;
  final String status;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PerformanceCycle({
    this.id,
    required this.cycleName,
    required this.startDate,
    required this.endDate,
    this.status = 'active',
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PerformanceCycle.fromMap(Map<String, dynamic> map) {
    return PerformanceCycle(
      id: map['id'] as int?,
      cycleName: map['cycle_name'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      status: map['status'] as String? ?? 'active',
      description: map['description'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'cycle_name': cycleName,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PerformanceCycle copyWith({
    int? id,
    String? cycleName,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PerformanceCycle(
      id: id ?? this.id,
      cycleName: cycleName ?? this.cycleName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط الدورة
  bool get isActive => status == 'active';

  /// التحقق من انتهاء الدورة
  bool get isCompleted => status == 'completed';

  /// التحقق من إلغاء الدورة
  bool get isCancelled => status == 'cancelled';
}

/// نموذج تفاصيل التقييم
class EvaluationDetail {
  final int? id;
  final int evaluationId;
  final int criteriaId;
  final double score;
  final String? comments;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EvaluationDetail({
    this.id,
    required this.evaluationId,
    required this.criteriaId,
    this.score = 0,
    this.comments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EvaluationDetail.fromMap(Map<String, dynamic> map) {
    return EvaluationDetail(
      id: map['id'] as int?,
      evaluationId: map['evaluation_id'] as int,
      criteriaId: map['criteria_id'] as int,
      score: (map['score'] as num?)?.toDouble() ?? 0,
      comments: map['comments'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'evaluation_id': evaluationId,
      'criteria_id': criteriaId,
      'score': score,
      'comments': comments,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EvaluationDetail copyWith({
    int? id,
    int? evaluationId,
    int? criteriaId,
    double? score,
    String? comments,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EvaluationDetail(
      id: id ?? this.id,
      evaluationId: evaluationId ?? this.evaluationId,
      criteriaId: criteriaId ?? this.criteriaId,
      score: score ?? this.score,
      comments: comments ?? this.comments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج تقييم الموظف (للتوافق مع الشاشات)
class EmployeeEvaluation {
  final int? id;
  final int employeeId;
  final int cycleId;
  final int evaluatorId;
  final double totalScore;
  final String status;
  final double overallRating;
  final String? strengths;
  final String? weaknesses;
  final String? developmentAreas;
  final String? goals;
  final String? selfEvaluation;
  final String? managerComments;
  final String? goalsNextPeriod;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeEvaluation({
    this.id,
    required this.employeeId,
    required this.cycleId,
    required this.evaluatorId,
    this.totalScore = 0,
    this.status = 'draft',
    this.overallRating = 0,
    this.strengths,
    this.weaknesses,
    this.developmentAreas,
    this.goals,
    this.selfEvaluation,
    this.managerComments,
    this.goalsNextPeriod,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeEvaluation.fromMap(Map<String, dynamic> map) {
    return EmployeeEvaluation(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      cycleId: map['cycle_id'] as int,
      evaluatorId: map['evaluator_id'] as int,
      totalScore: (map['total_score'] as num?)?.toDouble() ?? 0,
      status: map['status'] as String? ?? 'draft',
      overallRating: (map['overall_rating'] as num?)?.toDouble() ?? 0,
      strengths: map['strengths'] as String?,
      weaknesses: map['weaknesses'] as String?,
      developmentAreas: map['development_areas'] as String?,
      goals: map['goals'] as String?,
      selfEvaluation: map['self_evaluation'] as String?,
      managerComments: map['manager_comments'] as String?,
      goalsNextPeriod: map['goals_next_period'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'cycle_id': cycleId,
      'evaluator_id': evaluatorId,
      'total_score': totalScore,
      'status': status,
      'overall_rating': overallRating,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'development_areas': developmentAreas,
      'goals': goals,
      'self_evaluation': selfEvaluation,
      'manager_comments': managerComments,
      'goals_next_period': goalsNextPeriod,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeEvaluation copyWith({
    int? id,
    int? employeeId,
    int? cycleId,
    int? evaluatorId,
    double? totalScore,
    String? status,
    double? overallRating,
    String? strengths,
    String? weaknesses,
    String? developmentAreas,
    String? goals,
    String? selfEvaluation,
    String? managerComments,
    String? goalsNextPeriod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeEvaluation(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      cycleId: cycleId ?? this.cycleId,
      evaluatorId: evaluatorId ?? this.evaluatorId,
      totalScore: totalScore ?? this.totalScore,
      status: status ?? this.status,
      overallRating: overallRating ?? this.overallRating,
      strengths: strengths ?? this.strengths,
      weaknesses: weaknesses ?? this.weaknesses,
      developmentAreas: developmentAreas ?? this.developmentAreas,
      goals: goals ?? this.goals,
      selfEvaluation: selfEvaluation ?? this.selfEvaluation,
      managerComments: managerComments ?? this.managerComments,
      goalsNextPeriod: goalsNextPeriod ?? this.goalsNextPeriod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج معيار التقييم
class EvaluationCriteria {
  final int? id;
  final int cycleId;
  final String criteriaName;
  final double weight;
  final double maxScore;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EvaluationCriteria({
    this.id,
    required this.cycleId,
    required this.criteriaName,
    this.weight = 1.0,
    this.maxScore = 100,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EvaluationCriteria.fromMap(Map<String, dynamic> map) {
    return EvaluationCriteria(
      id: map['id'] as int?,
      cycleId: map['cycle_id'] as int,
      criteriaName: map['criteria_name'] as String,
      weight: (map['weight'] as num?)?.toDouble() ?? 1.0,
      maxScore: (map['max_score'] as num?)?.toDouble() ?? 100,
      description: map['description'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'cycle_id': cycleId,
      'criteria_name': criteriaName,
      'weight': weight,
      'max_score': maxScore,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EvaluationCriteria copyWith({
    int? id,
    int? cycleId,
    String? criteriaName,
    double? weight,
    double? maxScore,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EvaluationCriteria(
      id: id ?? this.id,
      cycleId: cycleId ?? this.cycleId,
      criteriaName: criteriaName ?? this.criteriaName,
      weight: weight ?? this.weight,
      maxScore: maxScore ?? this.maxScore,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج تقييم الأداء
class PerformanceEvaluation {
  final int? id;
  final int cycleId;
  final int employeeId;
  final int evaluatorId;
  final double totalScore;
  final String status;
  final String? selfEvaluation;
  final String? managerComments;
  final String? goalsNextPeriod;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PerformanceEvaluation({
    this.id,
    required this.cycleId,
    required this.employeeId,
    required this.evaluatorId,
    this.totalScore = 0,
    this.status = 'draft',
    this.selfEvaluation,
    this.managerComments,
    this.goalsNextPeriod,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PerformanceEvaluation.fromMap(Map<String, dynamic> map) {
    return PerformanceEvaluation(
      id: map['id'] as int?,
      cycleId: map['cycle_id'] as int,
      employeeId: map['employee_id'] as int,
      evaluatorId: map['evaluator_id'] as int,
      totalScore: (map['total_score'] as num?)?.toDouble() ?? 0,
      status: map['status'] as String? ?? 'draft',
      selfEvaluation: map['self_evaluation'] as String?,
      managerComments: map['manager_comments'] as String?,
      goalsNextPeriod: map['goals_next_period'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'cycle_id': cycleId,
      'employee_id': employeeId,
      'evaluator_id': evaluatorId,
      'total_score': totalScore,
      'status': status,
      'self_evaluation': selfEvaluation,
      'manager_comments': managerComments,
      'goals_next_period': goalsNextPeriod,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PerformanceEvaluation copyWith({
    int? id,
    int? cycleId,
    int? employeeId,
    int? evaluatorId,
    double? totalScore,
    String? status,
    String? selfEvaluation,
    String? managerComments,
    String? goalsNextPeriod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PerformanceEvaluation(
      id: id ?? this.id,
      cycleId: cycleId ?? this.cycleId,
      employeeId: employeeId ?? this.employeeId,
      evaluatorId: evaluatorId ?? this.evaluatorId,
      totalScore: totalScore ?? this.totalScore,
      status: status ?? this.status,
      selfEvaluation: selfEvaluation ?? this.selfEvaluation,
      managerComments: managerComments ?? this.managerComments,
      goalsNextPeriod: goalsNextPeriod ?? this.goalsNextPeriod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من اكتمال التقييم
  bool get isCompleted => status == 'completed';

  /// التحقق من الموافقة على التقييم
  bool get isApproved => status == 'approved';

  /// التحقق من كون التقييم مسودة
  bool get isDraft => status == 'draft';
}

/// نموذج المسار الوظيفي
class CareerPath {
  final int? id;
  final String pathName;
  final String? description;
  final int? departmentId;
  final List<CareerLevel> levels;
  final List<String> requirements;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerPath({
    this.id,
    required this.pathName,
    this.description,
    this.departmentId,
    this.levels = const [],
    this.requirements = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerPath.fromMap(Map<String, dynamic> map) {
    List<CareerLevel> levelsList = [];
    if (map['levels'] != null) {
      final levelsJson = map['levels'] as String;
      final List<dynamic> levelsData = jsonDecode(levelsJson);
      levelsList = levelsData.map((data) => CareerLevel.fromMap(data)).toList();
    }

    List<String> requirementsList = [];
    if (map['requirements'] != null) {
      final requirementsJson = map['requirements'] as String;
      final List<dynamic> requirementsData = jsonDecode(requirementsJson);
      requirementsList = requirementsData.cast<String>();
    }

    return CareerPath(
      id: map['id'] as int?,
      pathName: map['path_name'] as String,
      description: map['description'] as String?,
      departmentId: map['department_id'] as int?,
      levels: levelsList,
      requirements: requirementsList,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'path_name': pathName,
      'description': description,
      'department_id': departmentId,
      'levels': jsonEncode(levels.map((l) => l.toMap()).toList()),
      'requirements': jsonEncode(requirements),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerPath copyWith({
    int? id,
    String? pathName,
    String? description,
    int? departmentId,
    List<CareerLevel>? levels,
    List<String>? requirements,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerPath(
      id: id ?? this.id,
      pathName: pathName ?? this.pathName,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      levels: levels ?? this.levels,
      requirements: requirements ?? this.requirements,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج مستوى المسار الوظيفي
class CareerLevel {
  final int level;
  final String title;
  final List<String> requirements;

  const CareerLevel({
    required this.level,
    required this.title,
    this.requirements = const [],
  });

  factory CareerLevel.fromMap(Map<String, dynamic> map) {
    return CareerLevel(
      level: map['level'] as int,
      title: map['title'] as String,
      requirements: (map['requirements'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toMap() {
    return {'level': level, 'title': title, 'requirements': requirements};
  }
}

/// نموذج خطة التطوير الوظيفي
class CareerDevelopmentPlan {
  final int? id;
  final int employeeId;
  final int? careerPathId;
  final String? currentLevel;
  final String? targetLevel;
  final DateTime? targetDate;
  final List<String> developmentGoals;
  final String? progressNotes;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerDevelopmentPlan({
    this.id,
    required this.employeeId,
    this.careerPathId,
    this.currentLevel,
    this.targetLevel,
    this.targetDate,
    this.developmentGoals = const [],
    this.progressNotes,
    this.status = 'active',
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerDevelopmentPlan.fromMap(Map<String, dynamic> map) {
    List<String> goalsList = [];
    if (map['development_goals'] != null) {
      final goalsJson = map['development_goals'] as String;
      final List<dynamic> goalsData = jsonDecode(goalsJson);
      goalsList = goalsData.cast<String>();
    }

    return CareerDevelopmentPlan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      careerPathId: map['career_path_id'] as int?,
      currentLevel: map['current_level'] as String?,
      targetLevel: map['target_level'] as String?,
      targetDate: map['target_date'] != null
          ? DateTime.parse(map['target_date'] as String)
          : null,
      developmentGoals: goalsList,
      progressNotes: map['progress_notes'] as String?,
      status: map['status'] as String? ?? 'active',
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'career_path_id': careerPathId,
      'current_level': currentLevel,
      'target_level': targetLevel,
      'target_date': targetDate?.toIso8601String(),
      'development_goals': jsonEncode(developmentGoals),
      'progress_notes': progressNotes,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerDevelopmentPlan copyWith({
    int? id,
    int? employeeId,
    int? careerPathId,
    String? currentLevel,
    String? targetLevel,
    DateTime? targetDate,
    List<String>? developmentGoals,
    String? progressNotes,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerDevelopmentPlan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      careerPathId: careerPathId ?? this.careerPathId,
      currentLevel: currentLevel ?? this.currentLevel,
      targetLevel: targetLevel ?? this.targetLevel,
      targetDate: targetDate ?? this.targetDate,
      developmentGoals: developmentGoals ?? this.developmentGoals,
      progressNotes: progressNotes ?? this.progressNotes,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط الخطة
  bool get isActive => status == 'active';

  /// التحقق من اكتمال الخطة
  bool get isCompleted => status == 'completed';

  /// التحقق من إلغاء الخطة
  bool get isCancelled => status == 'cancelled';
}

/// نموذج مراجعة التطوير الوظيفي
class CareerReview {
  final int? id;
  final int employeeId;
  final int? planId;
  final DateTime reviewDate;
  final String reviewType;
  final double? progressPercentage;
  final String? employeeNotes;
  final String? managerNotes;
  final String? achievements;
  final String? challenges;
  final String? nextSteps;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerReview({
    this.id,
    required this.employeeId,
    this.planId,
    required this.reviewDate,
    this.reviewType = 'progress',
    this.progressPercentage,
    this.employeeNotes,
    this.managerNotes,
    this.achievements,
    this.challenges,
    this.nextSteps,
    this.status = 'active',
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerReview.fromMap(Map<String, dynamic> map) {
    return CareerReview(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      planId: map['plan_id'] as int?,
      reviewDate: DateTime.parse(map['review_date'] as String),
      reviewType: map['review_type'] as String? ?? 'progress',
      progressPercentage: map['progress_percentage'] as double?,
      employeeNotes: map['employee_notes'] as String?,
      managerNotes: map['manager_notes'] as String?,
      achievements: map['achievements'] as String?,
      challenges: map['challenges'] as String?,
      nextSteps: map['next_steps'] as String?,
      status: map['status'] as String? ?? 'active',
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'plan_id': planId,
      'review_date': reviewDate.toIso8601String(),
      'review_type': reviewType,
      'progress_percentage': progressPercentage,
      'employee_notes': employeeNotes,
      'manager_notes': managerNotes,
      'achievements': achievements,
      'challenges': challenges,
      'next_steps': nextSteps,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerReview copyWith({
    int? id,
    int? employeeId,
    int? planId,
    DateTime? reviewDate,
    String? reviewType,
    double? progressPercentage,
    String? employeeNotes,
    String? managerNotes,
    String? achievements,
    String? challenges,
    String? nextSteps,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerReview(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      planId: planId ?? this.planId,
      reviewDate: reviewDate ?? this.reviewDate,
      reviewType: reviewType ?? this.reviewType,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      employeeNotes: employeeNotes ?? this.employeeNotes,
      managerNotes: managerNotes ?? this.managerNotes,
      achievements: achievements ?? this.achievements,
      challenges: challenges ?? this.challenges,
      nextSteps: nextSteps ?? this.nextSteps,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
